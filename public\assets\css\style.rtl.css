/* RTL Specific Styles */

body.rtl {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    text-align: right;
    direction: rtl;
}

/* Header RTL Adjustments */
.rtl .header-actions {
    flex-direction: row-reverse;
}

.rtl .dropdown-menu {
    text-align: right;
}

.rtl .me-2, 
.rtl .me-3, 
.rtl .me-4 {
    margin-right: 0 !important;
}

.rtl .me-2 {
    margin-left: 0.5rem !important;
}

.rtl .me-3 {
    margin-left: 1rem !important;
}

.rtl .me-4 {
    margin-left: 1.5rem !important;
}

.rtl .ms-2,
.rtl .ms-3,
.rtl .ms-4 {
    margin-left: 0 !important;
}

.rtl .ms-2 {
    margin-right: 0.5rem !important;
}

.rtl .ms-3 {
    margin-right: 1rem !important;
}

.rtl .ms-4 {
    margin-right: 1.5rem !important;
}

.rtl .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.rtl .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* Form Controls RTL Adjustments */
.rtl .form-control {
    text-align: right;
}

.rtl .input-group > .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
}

.rtl .input-group > .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* List Groups RTL Adjustments */
.rtl .list-group {
    padding-right: 0;
}

/* Card RTL Adjustments */
.rtl .card-header,
.rtl .card-body,
.rtl .card-footer {
    text-align: right;
}

/* Modal RTL Adjustments */
.rtl .modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Breadcrumb RTL Adjustments */
.rtl .breadcrumb-item + .breadcrumb-item {
    padding-right: 0.5rem;
    padding-left: 0;
}

.rtl .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0;
}

/* Product Cards RTL Adjustments */
.rtl .product-card .product-actions {
    flex-direction: row-reverse;
}

/* Cart RTL Adjustments */
.rtl .cart-item .cart-item-details {
    text-align: right;
}

.rtl .cart-item .cart-item-price,
.rtl .cart-item .cart-item-total {
    text-align: left;
}

/* Checkout RTL Adjustments */
.rtl .checkout-steps .step-item:not(:last-child)::after {
    right: auto;
    left: 0;
}

/* User Dashboard RTL Adjustments */
.rtl .sidebar-nav .nav-link i {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Footer RTL Adjustments */
.rtl .footer-links {
    padding-right: 0;
}

.rtl .text-md-end {
    text-align: left !important;
}

/* Pagination RTL Adjustments */
.rtl .pagination {
    padding-right: 0;
}

.rtl .page-item:first-child .page-link {
    border-radius: 0 0.25rem 0.25rem 0;
}

.rtl .page-item:last-child .page-link {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Dropdown RTL Adjustments */
.rtl .dropdown-menu-end {
    right: auto;
    left: 0;
}

.rtl .dropdown-item {
    text-align: right;
}

/* Form Check RTL Adjustments */
.rtl .form-check {
    padding-right: 1.5em;
    padding-left: 0;
}

.rtl .form-check .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0;
}

/* Alert RTL Adjustments */
.rtl .alert-dismissible {
    padding-right: 1rem;
    padding-left: 4rem;
}

.rtl .alert-dismissible .btn-close {
    right: auto;
    left: 0;
}

/* Accordion RTL Adjustments */
.rtl .accordion-button::after {
    margin-right: auto;
    margin-left: 0;
}

/* Carousel RTL Adjustments */
.rtl .carousel-control-prev {
    right: 0;
    left: auto;
}

.rtl .carousel-control-next {
    right: auto;
    left: 0;
}

.rtl .carousel-indicators {
    right: 0;
    left: 0;
}

/* Toast RTL Adjustments */
.rtl .toast-header .btn-close {
    margin-right: auto;
    margin-left: -0.375rem;
}

/* Tooltip RTL Adjustments */
.rtl .tooltip {
    text-align: right;
}

/* Popover RTL Adjustments */
.rtl .popover {
    text-align: right;
}

.rtl .popover-header {
    text-align: right;
}

.rtl .popover-body {
    text-align: right;
}

/* Badge RTL Adjustments */
.rtl .position-absolute.top-0.start-100 {
    right: 100%;
    left: auto;
}

/* Progress RTL Adjustments */
.rtl .progress-bar {
    float: right;
}

/* Spinner RTL Adjustments */
.rtl .spinner-border,
.rtl .spinner-grow {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Custom RTL Adjustments */
.rtl .product-details .product-info {
    text-align: right;
}

.rtl .product-details .product-actions {
    flex-direction: row-reverse;
}

.rtl .review-item .review-header {
    flex-direction: row-reverse;
}

.rtl .review-item .review-rating {
    margin-right: 0;
    margin-left: 1rem;
}

.rtl .order-summary .summary-item {
    display: flex;
    justify-content: space-between;
}

.rtl .order-summary .summary-item .summary-value {
    text-align: left;
}

/* Mobile Responsiveness for RTL */
@media (max-width: 767.98px) {
    .rtl .text-md-end {
        text-align: right !important;
    }
    
    .rtl .navbar-nav {
        padding-right: 0;
    }
    
    .rtl .dropdown-menu {
        text-align: right;
    }
}
