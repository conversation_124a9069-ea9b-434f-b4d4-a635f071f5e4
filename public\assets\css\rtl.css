/* RTL Support for Arabic Language */

/* General RTL Styles */
[dir="rtl"] {
    text-align: right;
}

/* Flip floats */
[dir="rtl"] .float-start {
    float: right !important;
}

[dir="rtl"] .float-end {
    float: left !important;
}

/* Flip margins and paddings */
[dir="rtl"] .me-1,
[dir="rtl"] .me-2,
[dir="rtl"] .me-3,
[dir="rtl"] .me-4,
[dir="rtl"] .me-5 {
    margin-right: 0 !important;
}

[dir="rtl"] .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .ms-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .me-5 {
    margin-left: 3rem !important;
    margin-right: 0 !important;
}

/* Flip paddings */
[dir="rtl"] .ps-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .ps-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
}

[dir="rtl"] .pe-1 {
    padding-left: 0.25rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-4 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important;
}

[dir="rtl"] .pe-5 {
    padding-left: 3rem !important;
    padding-right: 0 !important;
}

/* Flip text alignment */
[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

/* Flip positions */
[dir="rtl"] .start-0 {
    right: 0 !important;
    left: auto !important;
}

[dir="rtl"] .end-0 {
    left: 0 !important;
    right: auto !important;
}

/* Navigation */
[dir="rtl"] .navigation__list {
    padding-right: 0;
}

/* Header tools */
[dir="rtl"] .header-tools__item {
    margin-left: 0;
    margin-right: 1.5rem;
}

[dir="rtl"] .header-tools__item:first-child {
    margin-right: 0;
}

/* Cart */
[dir="rtl"] .cart-amount {
    right: auto;
    left: -8px;
}

/* Product cards */
[dir="rtl"] .pc__btn-wl {
    right: auto;
    left: 0;
}

/* Form controls */
[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: -1.5em;
}

[dir="rtl"] .form-check-label {
    padding-left: 0;
    padding-right: 1.5em;
}

/* Breadcrumbs */
[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item {
    padding-left: 0;
    padding-right: 0.5rem;
}

/* Dropdown menus */
[dir="rtl"] .dropdown-menu {
    text-align: right;
}

/* Modal dialogs */
[dir="rtl"] .modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* List groups */
[dir="rtl"] .list-group {
    padding-right: 0;
}

/* Tables */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

/* Quantity control */
[dir="rtl"] .qty-control__reduce {
    left: auto;
    right: 0;
}

[dir="rtl"] .qty-control__increase {
    right: auto;
    left: 0;
}

/* WhatsApp button */
[dir="rtl"] .fab.fa-whatsapp {
    margin-right: 0 !important;
    margin-left: 8px !important;
}

/* Font family for Arabic */
[dir="rtl"] body {
    font-family: 'Cairo', 'Jost', sans-serif;
}

/* Fix for swiper slider */
[dir="rtl"] .swiper-container-rtl .swiper-button-next {
    left: 10px;
    right: auto;
}

[dir="rtl"] .swiper-container-rtl .swiper-button-prev {
    right: 10px;
    left: auto;
}

/* Fix for product details page */
[dir="rtl"] .product-single__meta-info .meta-item label {
    float: right;
    margin-left: 5px;
}

/* Fix for checkout form */
[dir="rtl"] .checkout-form label {
    text-align: right;
}

/* Fix for order confirmation page */
[dir="rtl"] .order-info__item label {
    float: right;
    margin-left: 10px;
}

/* Fix for admin panel */
[dir="rtl"] .admin-sidebar {
    right: 0;
    left: auto;
}

[dir="rtl"] .admin-content {
    margin-left: 0;
    margin-right: 250px;
}

/* Fix for mobile menu */
[dir="rtl"] .header-mobile__navigation {
    left: auto;
    right: 0;
}

/* Fix for search icon */
[dir="rtl"] .search-field__actor {
    left: auto;
    right: 0;
}

/* Fix for product filters */
[dir="rtl"] .filter-options {
    padding-right: 0;
}

[dir="rtl"] .filter-option input[type="checkbox"] {
    margin-right: 0;
    margin-left: 10px;
}

/* Fix for pagination */
[dir="rtl"] .pagination {
    padding-right: 0;
}

[dir="rtl"] .page-item:first-child .page-link {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .page-item:last-child .page-link {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

/* Fix for product details tabs */
[dir="rtl"] .nav-tabs {
    padding-right: 0;
}

/* Fix for product reviews */
[dir="rtl"] .customer-avatar {
    float: right;
    margin-right: 0;
    margin-left: 20px;
}

/* Fix for footer */
[dir="rtl"] .footer__social-links {
    padding-right: 0;
}

/* Fix for product gallery */
[dir="rtl"] .product-single__thumbnail {
    margin-right: 0;
    margin-left: 20px;
}

/* Fix for product price */
[dir="rtl"] .product-single__price .sale-price {
    margin-right: 0;
    margin-left: 10px;
}

/* Fix for product quantity */
[dir="rtl"] .product-single__addtocart .qty-control {
    margin-right: 0;
    margin-left: 15px;
}

/* Fix for wishlist button */
[dir="rtl"] .add-to-wishlist svg {
    margin-right: 0;
    margin-left: 5px;
}

/* Fix for cart table */
[dir="rtl"] .cart-table__product-name {
    text-align: right;
}

[dir="rtl"] .cart-table__product-price {
    text-align: left;
}

/* Fix for checkout summary */
[dir="rtl"] .checkout-summary__item-name {
    float: right;
}

[dir="rtl"] .checkout-summary__item-price {
    float: left;
}

/* Fix for order tracking */
[dir="rtl"] .order-tracking__step {
    text-align: right;
}

[dir="rtl"] .order-tracking__step-icon {
    margin-right: 0;
    margin-left: 15px;
}

/* Fix for account sidebar */
[dir="rtl"] .account-sidebar__menu {
    padding-right: 0;
}

/* Fix for account orders */
[dir="rtl"] .account-orders__id {
    text-align: right;
}

[dir="rtl"] .account-orders__status {
    text-align: left;
}

/* Fix for product grid */
[dir="rtl"] .products-grid {
    direction: rtl;
}

/* Fix for product sorting */
[dir="rtl"] .shop-acs__select {
    padding-right: 0.75rem;
    padding-left: 2rem;
    background-position: left 0.75rem center;
}

/* Fix for product filters */
[dir="rtl"] .shop-sidebar {
    left: auto;
    right: 0;
}

/* Fix for product pagination */
[dir="rtl"] .shop-pagination {
    direction: rtl;
}

/* Fix for product details */
[dir="rtl"] .product-single__prev-next {
    direction: rtl;
}

/* Fix for product tabs */
[dir="rtl"] .product-single__details-tab .nav-tabs {
    direction: rtl;
}

/* Fix for product reviews */
[dir="rtl"] .product-single__reviews-item {
    direction: rtl;
}

/* Fix for related products */
[dir="rtl"] .products-carousel .swiper-wrapper {
    direction: rtl;
}

/* Fix for footer widgets */
[dir="rtl"] .footer__widgets {
    direction: rtl;
}

/* Fix for footer bottom */
[dir="rtl"] .footer__bottom {
    direction: rtl;
}

/* Fix for mobile navigation */
[dir="rtl"] .mobile-nav-activator {
    margin-right: 0;
    margin-left: 1rem;
}

/* Fix for mobile logo */
[dir="rtl"] .header-mobile .logo {
    margin-right: 0;
    margin-left: auto;
}

/* Fix for mobile cart */
[dir="rtl"] .header-mobile .header-tools__cart {
    margin-left: 0;
    margin-right: auto;
}

/* Fix for mobile search */
[dir="rtl"] .header-mobile .search-field__input {
    padding-right: 1rem;
    padding-left: 3rem;
}

[dir="rtl"] .header-mobile .search-field__submit {
    right: auto;
    left: 1rem;
}

/* Fix for mobile menu */
[dir="rtl"] .header-mobile .navigation__list {
    padding-right: 0;
}

/* Language switcher */
[dir="rtl"] .language-switcher .dropdown-toggle img {
    margin-right: 0;
    margin-left: 5px;
}

[dir="rtl"] .language-switcher .dropdown-item img {
    margin-right: 0;
    margin-left: 8px;
}

/* Fix for mobile submenu */
[dir="rtl"] .header-mobile .navigation__link-arrow {
    right: auto;
    left: 0;
}

/* Fix for mobile social links */
[dir="rtl"] .header-mobile .social-links {
    padding-right: 0;
}

/* Fix for Cairo font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
