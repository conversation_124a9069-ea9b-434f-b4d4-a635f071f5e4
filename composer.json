{"name": "hodifa/kids-store", "type": "project", "description": "متجر الأطفال الإلكتروني - منصة تجارة إلكترونية متكاملة لمنتجات الأطفال", "keywords": ["laravel", "ecommerce", "kids-store", "arabic", "children-products"], "license": "MIT", "authors": [{"name": "حذيفة الحذيفي", "email": "<EMAIL>", "homepage": "https://www.hodifa-dev.com", "role": "Developer"}], "require": {"php": "^8.2", "intervention/image-laravel": "^1.2", "laravel/framework": "^11.9", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "surfsidemedia/shoppingcart": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}