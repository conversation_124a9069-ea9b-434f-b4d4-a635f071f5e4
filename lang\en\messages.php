<?php

return [
    // General
    'home' => 'Home',
    'shop' => 'Shop',
    'cart' => 'Cart',
    'about' => 'About',
    'contact' => 'Contact',
    'search' => 'Search',
    'search_placeholder' => 'Search products',
    'search_what' => 'What are you looking for?',
    'quicklinks' => 'Quicklinks',
    'my_account' => 'My Account',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    'dashboard' => 'Dashboard',
    'admin' => 'Admin',

    // Shop
    'products' => 'Products',
    'categories' => 'Categories',
    'brands' => 'Brands',
    'price' => 'Price',
    'color' => 'Color',
    'sizes' => 'Sizes',
    'min_price' => 'Min Price',
    'max_price' => 'Max Price',
    'show' => 'Show',
    'sort_by' => 'Sort by',
    'newest' => 'Newest',
    'oldest' => 'Oldest',
    'price_low_high' => 'Price: Low to High',
    'price_high_low' => 'Price: High to Low',
    'default' => 'Default',
    'date_new_old' => 'Date: New to Old',
    'date_old_new' => 'Date: Old to New',
    'view' => 'View',
    'filter' => 'Filter',
    'clear_all' => 'Clear All',
    'apply_filter' => 'Apply Filter',
    'in_stock' => 'In Stock',
    'out_of_stock' => 'Out of Stock',
    'add_to_cart' => 'Add to Cart',
    'go_to_cart' => 'Go to Cart',
    'add_to_wishlist' => 'Add to Wishlist',
    'remove_from_wishlist' => 'Remove from Wishlist',
    'view_details' => 'View Details',
    'related_products' => 'Related Products',
    'description' => 'Description',
    'reviews' => 'Reviews',
    'specifications' => 'Specifications',
    'quantity' => 'Quantity',
    'sku' => 'SKU',
    'availability' => 'Availability',
    'share' => 'Share',

    // Cart
    'your_cart' => 'Your Cart',
    'product' => 'Product',
    'unit_price' => 'Unit Price',
    'subtotal' => 'Subtotal',
    'total' => 'Total',
    'tax' => 'Tax',
    'discount' => 'Discount',
    'coupon_code' => 'Coupon Code',
    'apply_coupon' => 'Apply Coupon',
    'update_cart' => 'Update Cart',
    'checkout' => 'Checkout',
    'continue_shopping' => 'Continue Shopping',
    'cart_empty' => 'Your cart is empty',
    'cart_empty_message' => 'Looks like you haven\'t added any products to your cart yet.',
    'remove' => 'Remove',

    // Checkout
    'billing_details' => 'Billing Details',
    'shipping_details' => 'Shipping Details',
    'payment_method' => 'Payment Method',
    'order_summary' => 'Order Summary',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'city' => 'City',
    'state' => 'State',
    'zip' => 'ZIP Code',
    'country' => 'Country',
    'place_order' => 'Place Order',
    'order_notes' => 'Order Notes',
    'same_as_billing' => 'Same as billing address',

    // Order
    'order_confirmation' => 'Order Confirmation',
    'order_number' => 'Order Number',
    'order_date' => 'Order Date',
    'order_status' => 'Order Status',
    'order_details' => 'Order Details',
    'order_items' => 'Order Items',
    'shipping_address' => 'Shipping Address',
    'billing_address' => 'Billing Address',
    'payment_info' => 'Payment Information',
    'track_order' => 'Track Order',
    'confirm_via_whatsapp' => 'Confirm via WhatsApp',
    'whatsapp_button' => 'Confirm Order via WhatsApp',
    'ordered' => 'Ordered',
    'delivered' => 'Delivered',
    'canceled' => 'Canceled',

    // Account
    'account_info' => 'Account Information',
    'order_history' => 'Order History',
    'wishlist' => 'Wishlist',
    'addresses' => 'Addresses',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'save_changes' => 'Save Changes',
    'profile' => 'Profile',

    // Admin
    'dashboard' => 'Dashboard',
    'products_management' => 'Products Management',
    'categories_management' => 'Categories Management',
    'brands_management' => 'Brands Management',
    'orders_management' => 'Orders Management',
    'users_management' => 'Users Management',
    'coupons_management' => 'Coupons Management',
    'add_new' => 'Add New',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'status' => 'Status',
    'actions' => 'Actions',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'yes' => 'Yes',
    'no' => 'No',

    // Footer
    'newsletter' => 'Newsletter',
    'subscribe' => 'Subscribe',
    'subscribe_message' => 'Subscribe to our newsletter and get 10% off your first purchase',
    'subscribe_newsletter_text' => 'Subscribe to our newsletter to receive updates and exclusive offers',
    'your_email' => 'Your email address',
    'follow_us' => 'Follow Us',
    'copyright' => 'Copyright',
    'all_rights_reserved' => 'All rights reserved',
    'terms' => 'Terms & Conditions',
    'privacy_policy' => 'Privacy Policy',
    'faq' => 'FAQ',
    'contact_us' => 'Contact Us',

    // Messages
    'added_to_cart' => 'Product added to cart successfully',
    'removed_from_cart' => 'Product removed from cart successfully',
    'cart_updated' => 'Cart updated successfully',
    'added_to_wishlist' => 'Product added to wishlist successfully',
    'removed_from_wishlist' => 'Product removed from wishlist successfully',
    'coupon_applied' => 'Coupon applied successfully',
    'invalid_coupon' => 'Invalid coupon code',
    'order_placed' => 'Your order has been placed successfully',
    'profile_updated' => 'Profile updated successfully',
    'password_changed' => 'Password changed successfully',
    'address_added' => 'Address added successfully',
    'address_updated' => 'Address updated successfully',
    'address_deleted' => 'Address deleted successfully',
    'login_success' => 'Logged in successfully',
    'login_failed' => 'Invalid credentials',
    'logout_success' => 'Logged out successfully',
    'register_success' => 'Registration successful',
    'newsletter_subscribed' => 'Thank you for subscribing to our newsletter!',
    'newsletter_error' => 'An error occurred while processing your request. Please try again.',
    'newsletter_already_subscribed' => 'You are already subscribed to our newsletter.',

    // SEO
    'site_description' => 'An e-commerce store specializing in children\'s products offering a wide range of high-quality clothing, toys, and accessories for kids at affordable prices',
    'site_keywords' => 'kids store, children clothing, toys for kids, children accessories, baby products, gifts for kids, children essentials',
];
