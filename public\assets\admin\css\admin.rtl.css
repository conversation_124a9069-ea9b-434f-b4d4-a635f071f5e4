/* RTL Specific Styles for Admin Panel */

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    text-align: right;
    direction: rtl;
}

/* Header RTL Adjustments */
.header-right {
    margin-left: 0;
    margin-right: auto;
}

.header-left {
    margin-right: 0;
    margin-left: auto;
}

.dropdown-menu {
    text-align: right;
}

.dropdown-item {
    text-align: right;
}

/* Sidebar RTL Adjustments */
.sidebar .menu-box .menu-list .menu-item a .icon {
    margin-right: 0;
    margin-left: 10px;
}

.sidebar .menu-box .menu-list .menu-item.has-children .sub-menu {
    padding-left: 0;
    padding-right: 30px;
}

.sidebar .menu-box .menu-list .menu-item.has-children > a:after {
    right: auto;
    left: 15px;
    transform: rotate(180deg);
}

.sidebar .menu-box .menu-list .menu-item.has-children.active > a:after {
    transform: rotate(270deg);
}

/* Form Controls RTL Adjustments */
.form-control {
    text-align: right;
}

.input-group > .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group > .input-group-append > .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Card RTL Adjustments */
.card-header,
.card-body,
.card-footer {
    text-align: right;
}

/* Table RTL Adjustments */
.table th,
.table td {
    text-align: right;
}

.table .dropdown-menu {
    right: 0;
    left: auto;
}

/* Pagination RTL Adjustments */
.pagination {
    padding-right: 0;
}

.page-item:first-child .page-link {
    border-radius: 0 0.25rem 0.25rem 0;
}

.page-item:last-child .page-link {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Modal RTL Adjustments */
.modal-header .close {
    margin: -1rem auto -1rem -1rem;
}

.modal-footer {
    justify-content: flex-start;
}

.modal-footer > * {
    margin: 0.25rem 0 0.25rem 0.25rem;
}

/* Alert RTL Adjustments */
.alert-dismissible {
    padding-right: 1.25rem;
    padding-left: 4rem;
}

.alert-dismissible .close {
    right: auto;
    left: 0;
}

/* Form Check RTL Adjustments */
.form-check {
    padding-right: 1.25rem;
    padding-left: 0;
}

.form-check-input {
    margin-right: -1.25rem;
    margin-left: 0;
}

/* Button Group RTL Adjustments */
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn-group:not(:last-child) > .btn {
    border-radius: 0 0.25rem 0.25rem 0;
}

.btn-group > .btn:not(:first-child),
.btn-group > .btn-group:not(:first-child) > .btn {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Dropdown RTL Adjustments */
.dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

.dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

/* List Group RTL Adjustments */
.list-group {
    padding-right: 0;
}

/* Progress RTL Adjustments */
.progress-bar {
    float: right;
}

/* Custom RTL Adjustments for Admin Panel */
.box-heading .box-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0;
}

.box-heading .box-breadcrumb .breadcrumb-item + .breadcrumb-item {
    padding-right: 0.5rem;
    padding-left: 0;
}

.box-filters .search-form .button-search {
    right: auto;
    left: 0;
}

.box-filters .search-form input {
    padding-right: 15px;
    padding-left: 40px;
}

.box-filters .filter-item {
    margin-right: 0;
    margin-left: 20px;
}

.box-filters .filter-item:last-child {
    margin-left: 0;
}

.table-responsive .table .dropdown-menu {
    right: auto;
    left: 0;
}

.form-group label {
    text-align: right;
}

.form-check-inline {
    margin-right: 0;
    margin-left: 0.75rem;
}

.form-check-inline:last-child {
    margin-left: 0;
}

.custom-control {
    padding-right: 1.5rem;
    padding-left: 0;
}

.custom-control-label::before,
.custom-control-label::after {
    right: -1.5rem;
    left: auto;
}

.custom-switch {
    padding-right: 2.25rem;
    padding-left: 0;
}

.custom-switch .custom-control-label::before {
    right: -2.25rem;
    left: auto;
}

.custom-switch .custom-control-label::after {
    right: calc(-2.25rem + 2px);
    left: auto;
}

.custom-select {
    padding: 0.375rem 0.75rem 0.375rem 1.75rem;
    background-position: left 0.75rem center;
}

.input-group-prepend {
    margin-left: -1px;
    margin-right: 0;
}

.input-group-append {
    margin-right: -1px;
    margin-left: 0;
}

.input-group > .input-group-prepend > .btn,
.input-group > .input-group-prepend > .input-group-text,
.input-group > .input-group-append:not(:last-child) > .btn,
.input-group > .input-group-append:not(:last-child) > .input-group-text,
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
    border-radius: 0 0.25rem 0.25rem 0;
}

.input-group > .input-group-append > .btn,
.input-group > .input-group-append > .input-group-text,
.input-group > .input-group-prepend:not(:first-child) > .btn,
.input-group > .input-group-prepend:not(:first-child) > .input-group-text,
.input-group > .input-group-prepend:first-child > .btn:not(:first-child),
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Dashboard Cards RTL Adjustments */
.card .card-body .icon-big {
    float: right;
    margin-left: 15px;
    margin-right: 0;
}

.card .card-body .numbers {
    text-align: left;
}

/* Chart RTL Adjustments */
.chart-container {
    direction: ltr;
}

/* Responsive RTL Adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        right: -250px;
        left: auto;
    }
    
    .sidebar.active {
        right: 0;
        left: auto;
    }
    
    .content {
        margin-right: 0;
    }
    
    .content.active {
        margin-right: 250px;
        margin-left: 0;
    }
}
