body {
    zoom: 90% !important;
}

#logo_header_mobile {
    max-width: 190px;
}

.wg-table.table-all-user>* {
    min-width: 1260px;
}

.wgp-pagination svg {
    height: 20px;
}

.wgp-pagination p {
    font-size: 16px;
}

.pagination {
    margin-left: 30px;

    .page-link {
        padding: 1.1rem;
        font-size: 16px;
    }
}

.swal-button--confirm {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.swal-button--confirm.active,
.swal-button--confirm:active,
.show>.swal-button--confirm.dropdown-toggle,
.swal-button--confirm:hover {
    background-color: #b02a37 !important;
    border-color: #a52834 !important;
    color: #fff;
}

.swal-footer {
    text-align: center;
}

.table-bordered>:not(caption)>*>* {
    border-width: inherit;
    line-height: 32px;
    font-size: 14px;
    border: 1px solid #e1e1e1;
    vertical-align: middle;
}

.table-striped .image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    border-radius: 10px;
    overflow: hidden;
}

.table-striped {
    min-width: 1200px;
}

.table-striped th:nth-child(2),
.table-striped td:nth-child(2) {
    width: 250px;
    padding-bottom: 18px;
}

.pname {
    display: flex;
    gap: 13px;
}

.view-icon {
    width: 20px;
    margin: 0 auto;
}
.wg-table.table-all-user>* {
    min-width: 1260px;
}

/*------*/
.wgp-pagination svg {
    height: 20px;
}

.wgp-pagination p {
    font-size: 16px;
}

.pagination {
    margin-left: 30px;

    .page-link {
        padding: 1.1rem;
        font-size: 16px;
    }
}

.swal-button--confirm {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.swal-button--confirm.active,
.swal-button--confirm:active,
.show>.swal-button--confirm.dropdown-toggle,
.swal-button--confirm:hover {
    background-color: #b02a37 !important;
    border-color: #a52834 !important;
    color: #fff;
}

.swal-footer {
    text-align: center;
}

.table-bordered>:not(caption)>*>* {
    border-width: inherit;
    line-height: 32px;
    font-size: 14px;
    border: 1px solid #e1e1e1;
    vertical-align: middle;
}

.table-striped .image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    border-radius: 10px;
    overflow: hidden;
}

.table-striped {
    min-width: 1200px;
}

.table-striped th:nth-child(2),
.table-striped td:nth-child(2) {
    width: 250px;
    padding-bottom: 18px;
}

.pname {
    display: flex;
    gap: 13px;
}

.view-icon {
    width: 20px;
    margin: 0 auto;
}