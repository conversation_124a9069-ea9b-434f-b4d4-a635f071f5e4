@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon93d993d9.eot?rd1719');
  src:  url('fonts/icomoon93d993d9.eot?rd1719#iefix') format('embedded-opentype'),
    url('fonts/icomoon93d993d9.ttf?rd1719') format('truetype'),
    url('fonts/icomoon93d993d9.woff?rd1719') format('woff'),
    url('fonts/icomoon93d993d9.svg?rd1719#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-activity:before {
  content: "\e900";
}
.icon-airplay:before {
  content: "\e901";
}
.icon-alert-circle:before {
  content: "\e902";
}
.icon-alert-octagon:before {
  content: "\e903";
}
.icon-alert-triangle:before {
  content: "\e904";
}
.icon-align-center:before {
  content: "\e905";
}
.icon-align-justify:before {
  content: "\e906";
}
.icon-align-left:before {
  content: "\e907";
}
.icon-align-right:before {
  content: "\e908";
}
.icon-anchor:before {
  content: "\e909";
}
.icon-aperture:before {
  content: "\e90a";
}
.icon-archive:before {
  content: "\e90b";
}
.icon-arrow-down:before {
  content: "\e90c";
}
.icon-arrow-down-circle:before {
  content: "\e90d";
}
.icon-arrow-down-left:before {
  content: "\e90e";
}
.icon-arrow-down-right:before {
  content: "\e90f";
}
.icon-arrow-left:before {
  content: "\e910";
}
.icon-arrow-left-circle:before {
  content: "\e911";
}
.icon-arrow-right:before {
  content: "\e912";
}
.icon-arrow-right-circle:before {
  content: "\e913";
}
.icon-arrow-up:before {
  content: "\e914";
}
.icon-arrow-up-circle:before {
  content: "\e915";
}
.icon-arrow-up-left:before {
  content: "\e916";
}
.icon-arrow-up-right:before {
  content: "\e917";
}
.icon-at-sign:before {
  content: "\e918";
}
.icon-award:before {
  content: "\e919";
}
.icon-bar-chart:before {
  content: "\e91e";
}
.icon-bar-chart-2:before {
  content: "\e91f";
}
.icon-battery:before {
  content: "\e921";
}
.icon-battery-charging:before {
  content: "\e922";
}
.icon-bell:before {
  content: "\e923";
}
.icon-bell-off:before {
  content: "\e924";
}
.icon-bluetooth:before {
  content: "\e926";
}
.icon-bold:before {
  content: "\e927";
}
.icon-book:before {
  content: "\e928";
}
.icon-book-open:before {
  content: "\e929";
}
.icon-bookmark:before {
  content: "\e92a";
}
.icon-box:before {
  content: "\e92b";
}
.icon-briefcase:before {
  content: "\e92c";
}
.icon-calendar:before {
  content: "\e92d";
}
.icon-camera:before {
  content: "\e92e";
}
.icon-camera-off:before {
  content: "\e92f";
}
.icon-cast:before {
  content: "\e930";
}
.icon-check:before {
  content: "\e931";
}
.icon-check-circle:before {
  content: "\e932";
}
.icon-check-square:before {
  content: "\e933";
}
.icon-chevron-down:before {
  content: "\e934";
}
.icon-chevron-left:before {
  content: "\e935";
}
.icon-chevron-right:before {
  content: "\e936";
}
.icon-chevron-up:before {
  content: "\e937";
}
.icon-chevrons-down:before {
  content: "\e938";
}
.icon-chevrons-left:before {
  content: "\e939";
}
.icon-chevrons-right:before {
  content: "\e93a";
}
.icon-chevrons-up:before {
  content: "\e949";
}
.icon-chrome:before {
  content: "\e952";
}
.icon-circle:before {
  content: "\e954";
}
.icon-clipboard:before {
  content: "\e955";
}
.icon-clock:before {
  content: "\e956";
}
.icon-cloud:before {
  content: "\e957";
}
.icon-cloud-drizzle:before {
  content: "\e958";
}
.icon-cloud-lightning:before {
  content: "\e959";
}
.icon-cloud-off:before {
  content: "\e95a";
}
.icon-cloud-rain:before {
  content: "\e95b";
}
.icon-cloud-snow:before {
  content: "\e95c";
}
.icon-code:before {
  content: "\e95d";
}
.icon-codepen:before {
  content: "\e95e";
}
.icon-codesandbox:before {
  content: "\e95f";
}
.icon-coffee:before {
  content: "\e960";
}
.icon-columns:before {
  content: "\e961";
}
.icon-command:before {
  content: "\e962";
}
.icon-compass:before {
  content: "\e963";
}
.icon-copy:before {
  content: "\e964";
}
.icon-corner-down-left:before {
  content: "\e965";
}
.icon-corner-down-right:before {
  content: "\e966";
}
.icon-corner-left-down:before {
  content: "\e967";
}
.icon-corner-left-up:before {
  content: "\e968";
}
.icon-corner-right-down:before {
  content: "\e969";
}
.icon-corner-right-up:before {
  content: "\e96a";
}
.icon-corner-up-left:before {
  content: "\e96b";
}
.icon-corner-up-right:before {
  content: "\e96c";
}
.icon-cpu:before {
  content: "\e96d";
}
.icon-credit-card:before {
  content: "\e96e";
}
.icon-crop:before {
  content: "\e96f";
}
.icon-crosshair:before {
  content: "\e970";
}
.icon-database:before {
  content: "\e971";
}
.icon-delete:before {
  content: "\e972";
}
.icon-disc:before {
  content: "\e973";
}
.icon-dollar-sign:before {
  content: "\e974";
}
.icon-download:before {
  content: "\e975";
}
.icon-download-cloud:before {
  content: "\e976";
}
.icon-droplet:before {
  content: "\e977";
}
.icon-edit:before {
  content: "\e978";
}
.icon-edit-2:before {
  content: "\e979";
}
.icon-edit-3:before {
  content: "\e97a";
}
.icon-external-link:before {
  content: "\e97b";
}
.icon-eye:before {
  content: "\e97c";
}
.icon-eye-off:before {
  content: "\e97d";
}
.icon-facebook:before {
  content: "\e97e";
}
.icon-fast-forward:before {
  content: "\e97f";
}
.icon-feather:before {
  content: "\e980";
}
.icon-figma:before {
  content: "\e981";
}
.icon-file:before {
  content: "\e982";
}
.icon-file-minus:before {
  content: "\e983";
}
.icon-file-plus:before {
  content: "\e984";
}
.icon-file-text:before {
  content: "\e985";
}
.icon-film:before {
  content: "\e986";
}
.icon-filter:before {
  content: "\e987";
}
.icon-flag:before {
  content: "\e988";
}
.icon-folder:before {
  content: "\e989";
}
.icon-folder-minus:before {
  content: "\e98a";
}
.icon-folder-plus:before {
  content: "\e98b";
}
.icon-framer:before {
  content: "\e98c";
}
.icon-frown:before {
  content: "\e98d";
}
.icon-gift:before {
  content: "\e98e";
}
.icon-git-branch:before {
  content: "\e98f";
}
.icon-git-commit:before {
  content: "\e990";
}
.icon-git-merge:before {
  content: "\e991";
}
.icon-git-pull-request:before {
  content: "\e992";
}
.icon-github:before {
  content: "\e993";
}
.icon-gitlab:before {
  content: "\e994";
}
.icon-globe:before {
  content: "\e995";
}
.icon-grid:before {
  content: "\e996";
}
.icon-hard-drive:before {
  content: "\e997";
}
.icon-hash:before {
  content: "\e998";
}
.icon-headphones:before {
  content: "\e999";
}
.icon-heart:before {
  content: "\e99a";
}
.icon-help-circle:before {
  content: "\e99b";
}
.icon-hexagon:before {
  content: "\e99c";
}
.icon-home:before {
  content: "\e99d";
}
.icon-image:before {
  content: "\e99e";
}
.icon-inbox:before {
  content: "\e99f";
}
.icon-info:before {
  content: "\e9a0";
}
.icon-instagram:before {
  content: "\e9a1";
}
.icon-italic:before {
  content: "\e9a2";
}
.icon-key:before {
  content: "\e9a3";
}
.icon-layers:before {
  content: "\e9a4";
}
.icon-layout:before {
  content: "\e9a5";
}
.icon-life-buoy:before {
  content: "\e9a6";
}
.icon-link:before {
  content: "\e9a7";
}
.icon-link-2:before {
  content: "\e9a8";
}
.icon-linkedin:before {
  content: "\e9a9";
}
.icon-list:before {
  content: "\e9aa";
}
.icon-loader:before {
  content: "\e9ab";
}
.icon-lock:before {
  content: "\e9ac";
}
.icon-log-in:before {
  content: "\e9ad";
}
.icon-log-out:before {
  content: "\e9ae";
}
.icon-mail:before {
  content: "\e9af";
}
.icon-map:before {
  content: "\e9b0";
}
.icon-map-pin:before {
  content: "\e9b1";
}
.icon-maximize:before {
  content: "\e9b2";
}
.icon-maximize-2:before {
  content: "\e9b3";
}
.icon-meh:before {
  content: "\e9b4";
}
.icon-menu:before {
  content: "\e9b5";
}
.icon-message-circle:before {
  content: "\e9b6";
}
.icon-message-square:before {
  content: "\e9b7";
}
.icon-mic:before {
  content: "\e9b8";
}
.icon-mic-off:before {
  content: "\e9b9";
}
.icon-minimize:before {
  content: "\e9ba";
}
.icon-minimize-2:before {
  content: "\e9bb";
}
.icon-minus:before {
  content: "\e9bc";
}
.icon-minus-circle:before {
  content: "\e9bd";
}
.icon-minus-square:before {
  content: "\e9be";
}
.icon-monitor:before {
  content: "\e9bf";
}
.icon-moon:before {
  content: "\e9c0";
}
.icon-more-horizontal:before {
  content: "\e9c1";
}
.icon-more-vertical:before {
  content: "\e9c2";
}
.icon-mouse-pointer:before {
  content: "\e9c3";
}
.icon-move:before {
  content: "\e9c4";
}
.icon-music:before {
  content: "\e9c5";
}
.icon-navigation:before {
  content: "\e9c6";
}
.icon-navigation-2:before {
  content: "\e9c7";
}
.icon-octagon:before {
  content: "\e9c8";
}
.icon-package:before {
  content: "\e9c9";
}
.icon-paperclip:before {
  content: "\e9ca";
}
.icon-pause:before {
  content: "\e9cb";
}
.icon-pause-circle:before {
  content: "\e9cc";
}
.icon-pen-tool:before {
  content: "\e9cd";
}
.icon-percent:before {
  content: "\e9ce";
}
.icon-phone:before {
  content: "\e9cf";
}
.icon-phone-call:before {
  content: "\e9d0";
}
.icon-phone-forwarded:before {
  content: "\e9d1";
}
.icon-phone-incoming:before {
  content: "\e9d2";
}
.icon-phone-missed:before {
  content: "\e9d3";
}
.icon-phone-off:before {
  content: "\e9d4";
}
.icon-phone-outgoing:before {
  content: "\e9d5";
}
.icon-pie-chart:before {
  content: "\e9d6";
}
.icon-play:before {
  content: "\e9d7";
}
.icon-play-circle:before {
  content: "\e9d8";
}
.icon-plus:before {
  content: "\e9d9";
}
.icon-plus-circle:before {
  content: "\e9da";
}
.icon-plus-square:before {
  content: "\e9db";
}
.icon-pocket:before {
  content: "\e9dc";
}
.icon-power:before {
  content: "\e9dd";
}
.icon-printer:before {
  content: "\e9de";
}
.icon-radio:before {
  content: "\e9df";
}
.icon-refresh-ccw:before {
  content: "\e9e0";
}
.icon-refresh-cw:before {
  content: "\e9e1";
}
.icon-repeat:before {
  content: "\e9e2";
}
.icon-rewind:before {
  content: "\e9e3";
}
.icon-rotate-ccw:before {
  content: "\e9e4";
}
.icon-rotate-cw:before {
  content: "\e9e5";
}
.icon-rss:before {
  content: "\e9e6";
}
.icon-save:before {
  content: "\e9e7";
}
.icon-scissors:before {
  content: "\e9e8";
}
.icon-search:before {
  content: "\e9e9";
}
.icon-send:before {
  content: "\e9ea";
}
.icon-server:before {
  content: "\e9eb";
}
.icon-settings:before {
  content: "\e9ec";
}
.icon-share:before {
  content: "\e9ed";
}
.icon-share-2:before {
  content: "\e9ee";
}
.icon-shield:before {
  content: "\e9ef";
}
.icon-shield-off:before {
  content: "\e9f0";
}
.icon-shopping-bag:before {
  content: "\e9f1";
}
.icon-shopping-cart:before {
  content: "\e9f2";
}
.icon-shuffle:before {
  content: "\e9f3";
}
.icon-sidebar:before {
  content: "\e9f4";
}
.icon-skip-back:before {
  content: "\e9f5";
}
.icon-skip-forward:before {
  content: "\e9f6";
}
.icon-slack:before {
  content: "\e9f7";
}
.icon-slash:before {
  content: "\e9f8";
}
.icon-sliders:before {
  content: "\e9f9";
}
.icon-smartphone:before {
  content: "\e9fa";
}
.icon-smile:before {
  content: "\e9fb";
}
.icon-speaker:before {
  content: "\e9fc";
}
.icon-square:before {
  content: "\e9fd";
}
.icon-star:before {
  content: "\e9fe";
}
.icon-stop-circle:before {
  content: "\e9ff";
}
.icon-sun:before {
  content: "\ea00";
}
.icon-sunrise:before {
  content: "\ea01";
}
.icon-sunset:before {
  content: "\ea02";
}
.icon-tablet:before {
  content: "\ea03";
}
.icon-tag:before {
  content: "\ea04";
}
.icon-target:before {
  content: "\ea05";
}
.icon-terminal:before {
  content: "\ea06";
}
.icon-thermometer:before {
  content: "\ea07";
}
.icon-thumbs-down:before {
  content: "\ea08";
}
.icon-thumbs-up:before {
  content: "\ea09";
}
.icon-toggle-left:before {
  content: "\ea0a";
}
.icon-toggle-right:before {
  content: "\ea0b";
}
.icon-tool:before {
  content: "\ea0c";
}
.icon-trash:before {
  content: "\ea0d";
}
.icon-trash-2:before {
  content: "\ea0e";
}
.icon-trello:before {
  content: "\ea0f";
}
.icon-trending-down:before {
  content: "\ea10";
}
.icon-trending-up:before {
  content: "\ea11";
}
.icon-triangle:before {
  content: "\ea12";
}
.icon-truck:before {
  content: "\ea13";
}
.icon-tv:before {
  content: "\ea14";
}
.icon-twitch:before {
  content: "\ea15";
}
.icon-twitter:before {
  content: "\ea16";
}
.icon-type:before {
  content: "\ea17";
}
.icon-umbrella:before {
  content: "\ea18";
}
.icon-underline:before {
  content: "\ea19";
}
.icon-unlock:before {
  content: "\ea1a";
}
.icon-upload:before {
  content: "\ea1b";
}
.icon-upload-cloud:before {
  content: "\ea1c";
}
.icon-user:before {
  content: "\ea1d";
}
.icon-user-check:before {
  content: "\ea1e";
}
.icon-user-minus:before {
  content: "\ea1f";
}
.icon-user-plus:before {
  content: "\ea20";
}
.icon-user-x:before {
  content: "\ea21";
}
.icon-users:before {
  content: "\ea22";
}
.icon-video:before {
  content: "\ea23";
}
.icon-video-off:before {
  content: "\ea24";
}
.icon-voicemail:before {
  content: "\ea25";
}
.icon-volume:before {
  content: "\ea26";
}
.icon-volume-1:before {
  content: "\ea27";
}
.icon-volume-2:before {
  content: "\ea28";
}
.icon-volume-x:before {
  content: "\ea29";
}
.icon-watch:before {
  content: "\ea2a";
}
.icon-wifi:before {
  content: "\ea2b";
}
.icon-wifi-off:before {
  content: "\ea2c";
}
.icon-wind:before {
  content: "\ea2d";
}
.icon-x:before {
  content: "\ea2e";
}
.icon-x-circle:before {
  content: "\ea2f";
}
.icon-x-octagon:before {
  content: "\ea30";
}
.icon-x-square:before {
  content: "\ea31";
}
.icon-youtube:before {
  content: "\ea32";
}
.icon-zap:before {
  content: "\ea33";
}
.icon-zap-off:before {
  content: "\ea34";
}
.icon-zoom-in:before {
  content: "\ea35";
}
.icon-zoom-out:before {
  content: "\ea36";
}
.icon-noti-4:before {
  content: "\e91a";
}
.icon-noti-3:before {
  content: "\e91b";
}
.icon-noti-2:before {
  content: "\e91c";
}
.icon-noti-1:before {
  content: "\e91d";
}
.icon-menu-left:before {
  content: "\e920";
}
.icon-25:before {
  content: "\e93b";
}
.icon-24:before {
  content: "\e93c";
}
.icon-23:before {
  content: "\e93d";
}
.icon-22:before {
  content: "\e93e";
}
.icon-21:before {
  content: "\e93f";
}
.icon-20:before {
  content: "\e940";
}
.icon-19:before {
  content: "\e941";
}
.icon-18:before {
  content: "\e942";
}
.icon-17:before {
  content: "\e943";
}
.icon-16:before {
  content: "\e944";
}
.icon-14:before {
  content: "\e945";
}
.icon-13:before {
  content: "\e946";
}
.icon-12:before {
  content: "\e947";
}
.icon-11:before {
  content: "\e948";
}
.icon-10:before {
  content: "\ea37";
}
.icon-9:before {
  content: "\e94a";
}
.icon-8:before {
  content: "\e94b";
}
.icon-7:before {
  content: "\e94c";
}
.icon-6:before {
  content: "\e94d";
}
.icon-5:before {
  content: "\e94e";
}
.icon-4:before {
  content: "\e94f";
}
.icon-3:before {
  content: "\e950";
}
.icon-2:before {
  content: "\e951";
}
.icon-1:before {
  content: "\e953";
}
.icon-star1:before {
  content: "\e925";
}
