# تعليمات تشغيل متجر الأطفال الإلكتروني

**تم تطويره بواسطة: حذيفة الحذيفي**

---

## 🎉 تهانينا! تم إعداد المشروع بنجاح

تم رفع مشروع متجر الأطفال الإلكتروني بنجاح إلى GitHub وهو الآن يعمل على الخادم المحلي.

## 📋 معلومات المشروع

- **اسم المشروع**: متجر الأطفال الإلكتروني (دنيا الأطفال)
- **المطور**: حذيفة الحذيفي
- **رابط GitHub**: https://github.com/HA1234098765/dunya-alatfaal-shop.git
- **رابط الموقع المحلي**: http://127.0.0.1:8000

## 🔐 بيانات تسجيل الدخول

### المستخدم الإداري
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123
- **رابط لوحة التحكم**: http://127.0.0.1:8000/admin

### مستخدم تجريبي
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: user123

## 🚀 الخادم يعمل حالياً

الخادم يعمل حالياً على العنوان: **http://127.0.0.1:8000**

يمكنك:
- زيارة الموقع الرئيسي
- تسجيل الدخول كمدير
- إضافة منتجات جديدة
- إدارة الطلبات والعملاء

## 📁 هيكل المشروع

```
متجر الأطفال الإلكتروني/
├── app/                    # منطق التطبيق
├── database/              # قاعدة البيانات والمايجريشن
├── public/                # الملفات العامة والصور
├── resources/             # الواجهات والأصول
├── routes/                # مسارات التطبيق
├── README.md              # دليل المشروع
├── DEVELOPER.md           # معلومات المطور
├── CHANGELOG.md           # سجل التغييرات
└── CREDITS.md             # الشكر والتقدير
```

## 🛠️ الميزات المتاحة

### للعملاء
- ✅ تصفح المنتجات
- ✅ البحث والفلترة
- ✅ سلة التسوق
- ✅ نظام المراجعات
- ✅ قائمة الأمنيات

### للإدارة
- ✅ لوحة تحكم شاملة
- ✅ إدارة المنتجات
- ✅ إدارة الطلبات
- ✅ إدارة العملاء
- ✅ التقارير والإحصائيات

## 🔧 إعدادات إضافية

### تغيير إعدادات قاعدة البيانات
قم بتعديل ملف `.env`:
```
DB_DATABASE=kids_store
DB_USERNAME=root
DB_PASSWORD=
```

### تغيير إعدادات البريد الإلكتروني
```
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="متجر الأطفال"
```

## 📱 الوصول للموقع

1. **الصفحة الرئيسية**: http://127.0.0.1:8000
2. **تسجيل الدخول**: http://127.0.0.1:8000/login
3. **لوحة التحكم**: http://127.0.0.1:8000/admin
4. **المتجر**: http://127.0.0.1:8000/shop

## 🛡️ الأمان

- تم تطبيق معايير الأمان المتقدمة
- حماية من هجمات CSRF و XSS
- تشفير كلمات المرور
- نظام صلاحيات متدرج

## 📞 الدعم والتواصل

**المطور**: حذيفة الحذيفي  
**البريد الإلكتروني**: <EMAIL>  
**الموقع**: www.hodifa-dev.com

## 🎯 الخطوات التالية

1. **إضافة المنتجات**: ادخل إلى لوحة التحكم وأضف منتجات جديدة
2. **تخصيص التصميم**: عدل الألوان والخطوط حسب ذوقك
3. **إعداد الدفع**: اربط بوابات الدفع المحلية
4. **النشر**: انشر الموقع على خادم إنتاجي

## 🔄 إيقاف وتشغيل الخادم

### إيقاف الخادم
اضغط `Ctrl + C` في terminal الخادم

### تشغيل الخادم مرة أخرى
```bash
php artisan serve
```

---

<div align="center">
  <p><strong>تم التطوير بـ ❤️ بواسطة حذيفة الحذيفي</strong></p>
  <p>© 2024 جميع الحقوق محفوظة</p>
</div>
