# متجر الأطفال الإلكتروني

<div align="center">
  <h2>🛍️ منصة تجارة إلكترونية متكاملة لمنتجات الأطفال</h2>
  <p>تم تطويره بواسطة <strong>حذيفة الحذيفي</strong></p>
</div>

---

## 📋 نظرة عامة

متجر الأطفال الإلكتروني هو منصة تجارة إلكترونية شاملة ومتطورة مصممة خصيصاً لبيع منتجات الأطفال. تم بناء المشروع باستخدام Laravel 11 مع التركيز على الأمان والأداء وتجربة المستخدم المتميزة.

## ✨ المميزات الرئيسية

### 🛒 للعملاء
- **واجهة مستخدم عربية** - تصميم متجاوب وسهل الاستخدام
- **نظام بحث متقدم** - البحث بالفئات والعلامات التجارية والأسعار
- **سلة تسوق ذكية** - إدارة المنتجات والكميات بسهولة
- **نظام مراجعات** - تقييمات وآراء العملاء
- **تتبع الطلبات** - متابعة حالة الطلب في الوقت الفعلي
- **دفع آمن** - تكامل مع بوابات الدفع المحلية والعالمية

### 👨‍💼 للإدارة
- **لوحة تحكم شاملة** - إدارة المنتجات والطلبات والعملاء
- **إدارة المخزون** - تتبع المخزون والتنبيهات التلقائية
- **تقارير مفصلة** - إحصائيات المبيعات والأداء
- **إدارة المحتوى** - تحديث المحتوى والعروض
- **نظام أمان متقدم** - حماية البيانات والمعاملات

### 🔒 الأمان والحماية
- **تشفير البيانات** - حماية معلومات العملاء
- **مصادقة ثنائية** - أمان إضافي للحسابات
- **سجل العمليات** - تتبع جميع الأنشطة
- **حماية من الهجمات** - CSRF, XSS, SQL Injection

## 🛠️ التقنيات المستخدمة

- **Backend**: Laravel 11 (PHP 8.2+)
- **Frontend**: Bootstrap 5, JavaScript ES6
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Queue**: Laravel Queue
- **Storage**: Local/S3 Compatible
- **Payment**: تكامل مع بوابات الدفع المحلية

## 📦 المتطلبات

- PHP 8.2 أو أحدث
- MySQL 8.0 أو أحدث
- Composer
- Node.js & NPM
- Redis (اختياري للكاش)

## 🚀 التثبيت والإعداد

```bash
# استنساخ المشروع
git clone https://github.com/hodifa/kids-store.git
cd kids-store

# تثبيت التبعيات
composer install
npm install

# إعداد البيئة
cp .env.example .env
php artisan key:generate

# إعداد قاعدة البيانات
php artisan migrate --seed

# بناء الأصول
npm run build

# تشغيل الخادم
php artisan serve
```

## 📱 لقطات الشاشة

### الصفحة الرئيسية
- تصميم عصري وجذاب
- عرض المنتجات المميزة
- قسم العروض الخاصة

### صفحة المنتج
- صور عالية الجودة
- تفاصيل شاملة
- مراجعات العملاء

### لوحة التحكم
- إحصائيات شاملة
- إدارة سهلة
- تقارير مفصلة

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير المشروع:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📞 التواصل

**المطور**: حذيفة الحذيفي
**البريد الإلكتروني**: <EMAIL>
**الموقع**: [www.hodifa-dev.com](https://www.hodifa-dev.com)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

<div align="center">
  <p>تم التطوير بـ ❤️ بواسطة حذيفة الحذيفي</p>
  <p>© 2024 جميع الحقوق محفوظة</p>
</div>
